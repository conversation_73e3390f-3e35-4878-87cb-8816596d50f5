/* Русский комментарий: Определяем RGB цвета для свечения */
:root {
    --glow-rgb-gray-200: 229, 231, 235; /* Обычное качество */
    --glow-rgb-green-400: 74, 222, 128; /* Необычное качество */
    --glow-rgb-blue-400: 96, 165, 250; /* Редкое качество */
    --glow-rgb-purple-400: 192, 132, 252; /* Эпическое качество */
    --glow-rgb-orange-400: 251, 146, 60; /* Легендарное качество */
    --glow-rgb-pink-400: 244, 114, 182; /* Мифическое качество */
    --glow-rgb-yellow-400: 250, 204, 21; /* Божественное качество */

    /* Русский комментарий: RGB цвета для свечения валют */
    --glow-rgb-gold: 229, 183, 105; /* Золото */
    --glow-rgb-silver: 192, 192, 192; /* Серебро */
    --glow-rgb-bronze: 205, 127, 50; /* Бронза */

    /* Русский комментарий: RGB цвета для свечения предметов по качеству в логах кузнеца */
    --glow-rgb-common: 128, 128, 128; /* Обычное качество - серое */
    --glow-rgb-uncommon: 0, 255, 0; /* Необычное качество - зеленое */
    --glow-rgb-rare: 0, 128, 255; /* Редкое качество - синее */
    --glow-rgb-epic: 128, 0, 255; /* Эпическое качество - фиолетовое */
    --glow-rgb-legendary: 255, 128, 0; /* Легендарное качество - оранжевое */
}

/* Русский комментарий: Контейнер для эффекта свечения с относительным позиционированием */
.animate-breathing-glow {
    position: relative;
    z-index: 1;
    /* Русский комментарий: Устанавливаем переменную --glow-rgb-color, которая будет использоваться в анимации */
    --glow-rgb-color: var(--glow-rgb-current, var(--glow-rgb-green-400));
    /* Русский комментарий: Включаем аппаратное ускорение для более плавной анимации */
    will-change: filter;
    /* Русский комментарий: Первая анимация для основного элемента */
    animation: pulse-glow 6s ease-in-out infinite;
}

/* Русский комментарий: Создаем псевдоэлемент для дополнительного слоя свечения */
.animate-breathing-glow::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: inherit;
    z-index: -1;
    background: rgba(var(--glow-rgb-color), 0.25);
    filter: blur(4px);
    opacity: 0;
    /* Русский комментарий: Вторая анимация для псевдоэлемента с другой длительностью и задержкой */
    animation: breath-effect 5.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Русский комментарий: Усиливаем сияние для редких и выше качеств предметов */
.glow-color-orange-400::before,
.glow-color-purple-400::before,
.glow-color-pink-400::before,
.glow-color-yellow-400::before {
    background: rgba(var(--glow-rgb-color), 0.35);
    filter: blur(5px);
}

/* Русский комментарий: Плавная анимация основного объекта с эффектом фильтра свечения */
@keyframes pulse-glow {
    0%,
    100% {
        filter: drop-shadow(0 0 2px rgba(var(--glow-rgb-color), 0.4));
    }
    25% {
        filter: drop-shadow(0 0 4px rgba(var(--glow-rgb-color), 0.5));
    }
    50% {
        filter: drop-shadow(0 0 6px rgba(var(--glow-rgb-color), 0.6))
            brightness(1.05);
    }
    75% {
        filter: drop-shadow(0 0 4px rgba(var(--glow-rgb-color), 0.5));
    }
}

/* Русский комментарий: Анимация дыхания для псевдоэлемента */
@keyframes breath-effect {
    0%,
    100% {
        opacity: 0;
        transform: scale(0.95);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* Русский комментарий: Вспомогательные классы для установки текущего цвета свечения */
.glow-color-gray-200 {
    --glow-rgb-current: var(--glow-rgb-gray-200);
}
.glow-color-green-400 {
    --glow-rgb-current: var(--glow-rgb-green-400);
}
.glow-color-blue-400 {
    --glow-rgb-current: var(--glow-rgb-blue-400);
}
.glow-color-purple-400 {
    --glow-rgb-current: var(--glow-rgb-purple-400);
}
.glow-color-orange-400 {
    --glow-rgb-current: var(--glow-rgb-orange-400);
}
.glow-color-pink-400 {
    --glow-rgb-current: var(--glow-rgb-pink-400);
}
.glow-color-yellow-400 {
    --glow-rgb-current: var(--glow-rgb-yellow-400);
}

/* Русский комментарий: Классы для свечения валют */
.glow-color-gold {
    --glow-rgb-current: var(--glow-rgb-gold);
}
.glow-color-silver {
    --glow-rgb-current: var(--glow-rgb-silver);
}
.glow-color-bronze {
    --glow-rgb-current: var(--glow-rgb-bronze);
}

/* Русский комментарий: Классы для свечения предметов по качеству в логах кузнеца */
.glow-quality-common {
    --glow-rgb-current: var(--glow-rgb-common);
}
.glow-quality-uncommon {
    --glow-rgb-current: var(--glow-rgb-uncommon);
}
.glow-quality-rare {
    --glow-rgb-current: var(--glow-rgb-rare);
}
.glow-quality-epic {
    --glow-rgb-current: var(--glow-rgb-epic);
}
.glow-quality-legendary {
    --glow-rgb-current: var(--glow-rgb-legendary);
}

/* Русский комментарий: Дополнительные варианты анимации с разной скоростью и интенсивностью */
.glow-slow {
    animation-duration: 8s;
}
.glow-fast {
    animation-duration: 4s;
}
.glow-intense::before {
    background: rgba(var(--glow-rgb-color), 0.4);
    filter: blur(6px);
}
.glow-subtle::before {
    background: rgba(var(--glow-rgb-color), 0.15);
    filter: blur(3px);
}

/* Русский комментарий: Специальные стили для свечения валют в журнале боя */
.currency-glow {
    position: relative;
    display: inline-block;
}

.currency-glow::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: rgba(var(--glow-rgb-current), 0.3);
    border-radius: 50%;
    filter: blur(4px);
    z-index: -1;
    animation: currencyPulse 2s ease-in-out infinite;
}

/* Русский комментарий: Анимация пульсации для валют */
@keyframes currencyPulse {
    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

/* Русский комментарий: Специальные стили для свечения иконок предметов в логах кузнеца */
.item-icon-glow {
    position: relative;
    display: inline-block;
    /* Русский комментарий: Тонкое свечение с низкой прозрачностью */
    box-shadow: 0 0 3px
        rgba(var(--glow-rgb-current, var(--glow-rgb-common)), 0.4);
    border-radius: 2px;
    transition: box-shadow 0.3s ease;
}

/* Русский комментарий: Усиленное свечение для редких и выше качеств */
.item-icon-glow.glow-quality-rare,
.item-icon-glow.glow-quality-epic,
.item-icon-glow.glow-quality-legendary {
    box-shadow: 0 0 4px rgba(var(--glow-rgb-current), 0.5);
}

/* Русский комментарий: Анимация пульсации для легендарных предметов */
.item-icon-glow.glow-quality-legendary {
    animation: legendaryPulse 3s ease-in-out infinite;
}

@keyframes legendaryPulse {
    0%,
    100% {
        box-shadow: 0 0 4px rgba(var(--glow-rgb-legendary), 0.5);
    }
    50% {
        box-shadow: 0 0 6px rgba(var(--glow-rgb-legendary), 0.7);
    }
}

/* Русский комментарий: Улучшенная анимация свечения для экипировки */
/* Основной класс с более мягким эффектом дыхания */
.animate-equipment-glow {
    position: relative;
    z-index: 1;
    /* Русский комментарий: Устанавливаем переменную --glow-rgb-color для анимации */
    --glow-rgb-color: var(--glow-rgb-current, var(--glow-rgb-green-400));
    /* Русский комментарий: Включаем аппаратное ускорение */
    will-change: filter;
    /* Русский комментарий: Более мягкая анимация без затухания иконки */
    animation: equipment-glow-pulse 4s ease-in-out infinite;
}

/* Русский комментарий: Псевдоэлемент для внешнего свечения */
.animate-equipment-glow::before {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: inherit;
    z-index: -1;
    background: rgba(var(--glow-rgb-color), 0.3);
    filter: blur(5px);
    /* Русский комментарий: Начальная прозрачность выше, чтобы не затухало сильно */
    opacity: 0.6;
    /* Русский комментарий: Мягкая анимация дыхания */
    animation: equipment-breath-effect 4s ease-in-out infinite;
}

/* Русский комментарий: Усиленное свечение для редких и выше качеств */
.animate-equipment-glow.glow-color-blue-400::before,
.animate-equipment-glow.glow-color-purple-400::before,
.animate-equipment-glow.glow-color-orange-400::before {
    background: rgba(var(--glow-rgb-color), 0.4);
    filter: blur(6px);
    opacity: 0.7;
}

/* Русский комментарий: Анимация основного элемента - только легкое усиление без затухания */
@keyframes equipment-glow-pulse {
    0%,
    100% {
        filter: drop-shadow(0 0 3px rgba(var(--glow-rgb-color), 0.5));
    }
    50% {
        filter: drop-shadow(0 0 5px rgba(var(--glow-rgb-color), 0.7))
            brightness(1.02);
    }
}

/* Русский комментарий: Анимация псевдоэлемента - мягкое дыхание */
@keyframes equipment-breath-effect {
    0%,
    100% {
        opacity: 0.6;
        transform: scale(0.98);
    }
    50% {
        opacity: 0.9;
        transform: scale(1.03);
    }
}
