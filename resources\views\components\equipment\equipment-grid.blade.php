{{--
    Компонент сетки экипировки
    Отображает все слоты снаряжения в организованной сетке
--}}
@props([
    'equippedItems' => collect(),
    'isOwner' => true,
    'routePrefix' => 'user.equipped-item.details'
])



<div class="equipment-grid">
    @if($equippedItems->isEmpty())
        <div
            class="bg-[#3b3a33]/70 border border-[#a6925e]/50 rounded-lg p-8 shadow-lg text-center mt-6 backdrop-blur-sm">
            <p class="text-[#d9d3b8] italic">Ваши слоты снаряжения пусты.</p>
            <a href="{{ route('inventory.index') }}"
                class="mt-4 inline-block bg-[#a6925e] text-[#2f2d2b] py-2 px-4 rounded shadow-md hover:bg-[#d4b781] transition duration-300 text-sm font-medium">
                Перейти в рюкзак
            </a>
        </div>
    @else
        <div class="grid grid-cols-1 gap-4 mb-6 mt-2">
            @foreach($equippedItems as $equippedItem)
                @php
                    // Получаем сам предмет из связи
                    $item = $equippedItem->item;
                    // Определяем цвета и классы для качества
                    $qualityClassMap = [
                        'Обычное' => 'common',
                        'Необычное' => 'uncommon',
                        'Редкое' => 'rare',
                        'Эпическое' => 'epic',
                        'Легендарное' => 'legendary',
                    ];
                    $qualitySlug = $qualityClassMap[$item->quality] ?? 'common';
                    $qualityTextColor = $qualitySlug . '-text'; // Для текста, если используется style
                    $qualityBgColor = 'bg-' . $qualitySlug;     // Для фона точки

                    // Русский комментарий: Определение цвета качества предмета для свечения (как в инвентаре)
                    $qualityColors = [
                        'Обычное' => 'text-gray-200',
                        'Необычное' => 'text-green-400',
                        'Редкое' => 'text-blue-400',
                        'Эпическое' => 'text-purple-400',
                        'Легендарное' => 'text-orange-400',
                    ];
                    $qualityColor = $qualityColors[$item->quality] ?? 'text-gray-200';
                    // Русский комментарий: Определение класса для цвета свечения
                    $glowColorClass = str_replace('text-', 'glow-color-', $qualityColor);

                    // GS предмета (если есть)
                    $itemGs = $item->calculated_gs ?? 0; // Предполагаем, что GS рассчитывается в модели Item
                @endphp

                <div
                    class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02] group">
                    <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>

                    <div
                        class="absolute inset-0 rounded-lg border-2 border-[#a6925e]/70 pointer-events-none group-hover:border-[#e5b769]/90 transition-colors duration-300">
                    </div>
                    <div
                        class="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50 group-hover:opacity-80 transition-opacity duration-300">
                    </div>
                    <div
                        class="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50 group-hover:opacity-80 transition-opacity duration-300">
                    </div>

                    <div class="relative p-3">
                        <div class="flex items-start space-x-3 mb-2.5">
                            {{-- Русский комментарий: Контейнер иконки с эффектом свечения по качеству предмета --}}
                            <div class="relative flex-shrink-0 animate-breathing-glow {{ $glowColorClass }}">
                                {{-- Убираем все рамки и фоновые контейнеры, оставляем только чистое изображение --}}
                                <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                    alt="{{ $item->name }}"
                                    class="w-16 h-16 object-contain transform group-hover:scale-105 transition-transform duration-300"
                                    style="image-rendering: crisp-edges;">
                                @if($itemGs > 0)
                                    <div
                                        class="absolute -bottom-1 -right-1 px-1 py-0 bg-[#2a2721] text-green-400 text-[10px] font-bold rounded border border-[#a6925e]/70 shadow-sm">
                                        +{{ $itemGs }}
                                    </div>
                                @endif
                            </div>

                            <div class="flex-1 min-w-0">
                                <a href="{{ route('user.equipped-item.details', $equippedItem->id) }}"
                                    class="text-base font-bold {{ $qualityTextColor }} leading-tight mb-0.5 block truncate hover:text-yellow-300 transition-colors duration-200 text-shadow-sm"
                                    title="{{ $item->name }}">
                                    {{ $item->name }}
                                </a>

                                <div class="flex items-center text-xs mb-1 text-[#c7bfad] space-x-2">
                                    <span
                                        class="inline-flex items-center px-1.5 py-0.5 bg-[#38352c]/80 rounded border border-[#514b3c]/60">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5 text-[#a19a8a]"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 7a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2h6zM12 11h.01" />
                                        </svg>
                                        {{ $equippedItem->slot }}
                                    </span>
                                    <div class="inline-flex items-center">
                                        <div class="w-2 h-2 rounded-full mr-1 {{ $qualityBgColor }}"></div>
                                        <span class="{{ $qualityTextColor }}">{{ $item->quality }}</span>
                                    </div>
                                </div>
                                <div class="text-xs text-[#a19a8a]">
                                    <i class="fas fa-shield-alt text-[10px] mr-0.5 opacity-70"></i>
                                    Прочность: <span
                                        class="font-medium text-[#c7bfad]">{{ $item->durability ?? $item->base_max_durability }}/{{ $item->base_max_durability }}</span>
                                </div>

                                @if($equippedItem->activeEffects && $equippedItem->activeEffects->count() > 0)
                                    <div class="mt-1 text-xs">
                                        <div class="flex items-center">
                                            <span class="text-[#ffd700] text-[10px] mr-1">✨</span>
                                            <span class="text-[#ffd700]">Активные эффекты:</span>
                                        </div>
                                        <div class="pl-2 mt-0.5 space-y-1">
                                            @foreach($equippedItem->activeEffects as $effect)
                                                <div class="flex items-center justify-between">
                                                    <span class="text-[#d9d3b8]">{{ $effect->effect_name }}</span>
                                                    <span
                                                        class="text-[#a6925e] text-[9px]">{{ $effect->getRemainingDuration() }}
                                                        сек.</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-2 mt-2">
                            <button onclick="unequipItem({{ $equippedItem->id }})" title="Снять предмет"
                                class="w-full bg-gradient-to-b from-red-700/80 to-red-900/80 text-red-100 py-1.5 px-2 text-sm font-medium rounded shadow-md hover:from-red-600 hover:to-red-800 transition-all duration-300 border border-red-500/70 flex items-center justify-center focus:outline-none focus:ring-1 focus:ring-red-400">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                Снять
                            </button>

                            <a href="{{ route('user.equipped-item.details', $equippedItem) }}"
                                class="w-full bg-gradient-to-b from-[#514b3c]/90 to-[#38352c]/90 text-[#d9d3b8] py-1.5 px-2 text-sm font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c]/80 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                    <path fill-rule="evenodd"
                                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                Подробнее
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>
