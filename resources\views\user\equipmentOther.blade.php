<!DOCTYPE html>
<html lang="en">
{{-- Используем данные ТЕКУЩЕГО пользователя для хедера/футера --}}
@php $currentAuthUser = Auth::user();
$currentAuthUserProfile = $currentAuthUser->profile; @endphp

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}"> {{-- Оставляем для возможных будущих AJAX запросов --}}
    <title>Снаряжение: {{ $user->name }} - {{ config('app.name', 'Laravel') }}</title> {{-- Имя просматриваемого
    пользователя --}}

    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/css/glow-effects.css', 'resources/css/equipment/equipment-main.css', 'resources/css/equipment/equipment-animations.css', 'resources/css/equipment/item-quality.css'])

    <style>
        /* Цвета для качества предмета (можно вынести в app.css) */
        .common-text {
            color: #a0aec0;
        }

        /* gray-500 */
        .uncommon-text {
            color: #48bb78;
        }

        /* green-500 */
        .rare-text {
            color: #4299e1;
        }

        /* blue-500 */
        .epic-text {
            color: #9f7aea;
        }

        /* purple-500 */
        .legendary-text {
            color: #ed8936;
        }

        /* orange-500 */

        .text-shadow-sm {
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
        }

        .bg-common {
            background-color: #a0aec0;
        }

        .bg-uncommon {
            background-color: #48bb78;
        }

        .bg-rare {
            background-color: #4299e1;
        }

        .bg-epic {
            background-color: #9f7aea;
        }

        .bg-legendary {
            background-color: #ed8936;
        }

        /* Стили для сравнения СТАТОВ */
        .stat-comparison-positive { color: #48bb78; /* green-500 */ }
        .stat-comparison-negative { color: #8B4513; /* saddle brown - коричневый */ }
        .stat-comparison-neutral { color: #a0aec0;  /* gray-500 - Серый для нулевой разницы */ }

        /* Оставим на всякий случай */
    </style>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- Основной контейнер --}}
    <div
        class="container max-w-md mx-auto px-1 py-0 flex-grow bg-gradient-to-b from-[#4a4a3d]/90 to-[#3b3a33]/95 border-2 border-[#a6925e] rounded-lg shadow-xl backdrop-blur-sm">

        {{-- Хедер с данными ТЕКУЩЕГО пользователя ($currentAuthUser) --}}
        <div class="flex justify-between items-center text-[#d9d3b8]  rounded-md pt-1.5 shadow-inner">
            {{-- HP --}}
            <div class="flex items-center">
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                    <span class="text-[#FF6347] text-xs">❤️</span></div>
                <div class="flex flex-col">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full"
                            style="width: calc({{ $currentAuthUserProfile->hp }}/{{ $currentAuthUserProfile->max_hp }}*100%)">
                        </div>
                    </div>
                    <span
                        class="text-[#e5b769] text-[12px]">{{ $currentAuthUserProfile->hp }}/{{ $currentAuthUserProfile->max_hp }}</span>
                </div>
            </div>
            {{-- Уведомления --}}
            <div class="flex space-x-1">
                <a href="/mail" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📩</span></div>
                </a>
                <a href="/events" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">🎉</span></div>
                </a>
                <a href="/quests" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📜</span></div>
                </a>
            </div>
            {{-- MP --}}
            <div class="flex items-center">
                <div class="flex flex-col items-end">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full"
                            style="width: calc({{ $currentAuthUserProfile->mp }}/{{ $currentAuthUserProfile->max_mp }}*100%)">
                        </div>
                    </div>
                    <span
                        class="text-[#e5b769] text-[12px]">{{ $currentAuthUserProfile->mp }}/{{ $currentAuthUserProfile->max_mp }}</span>
                </div>
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] ml-1">
                    <span class="text-[#1E90FF] text-xs">🔮</span></div>
            </div>
        </div>
        {{-- Валюта ТЕКУЩЕГО пользователя --}}
        <div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5">
            <div class="flex gap-1 items-center ">
                <div class="flex items-center "><img src="{{ asset('assets/goldIcon.png') }}" alt="Золото"
                        class="w-4 h-4"><span class="text-sm font-medium text-[#e5b769]">
                        {{ number_format($currentAuthUserProfile->gold, 0, ',', ' ') }}</span></div>
                <div class="flex items-center "><img src="{{ asset('assets/silverIcon.png') }}" alt="Серебро"
                        class="w-4 h-4"><span class="text-sm font-medium text-[#c0c0c0]">
                        {{ number_format($currentAuthUserProfile->silver, 0, ',', ' ') }}</span></div>
                <div class="flex items-center "><img src="{{ asset('assets/bronzeIcon.png') }}" alt="Бронза"
                        class="w-4 h-4"><span class="text-sm font-medium text-[#cd7f32]">
                        {{ number_format($currentAuthUserProfile->bronze, 0, ',', ' ') }}</span></div>
            </div>
        </div>

        {{-- Заголовок и хлебные крошки --}}
        <div class="mb-4">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
            <p
                class="block text-center text-white py-2 px-8 rounded-md shadow-lg bg-gradient-to-b from-[#3e815f]/90 to-[#2E8B57]/90 relative border-t border-b border-green-400/40 text-shadow-sm font-semibold text-lg">
                Снаряжение: {{ $user->name }} {{-- Оставляем имя просматриваемого пользователя --}}
                <span
                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-200 font-mono tracking-wider bg-[#252117]/60 px-2 py-0.5 rounded-md border border-[#514b3c]/50 shadow-inner">
                    [{{ $equippedCount }}/{{ $totalSlots }}] {{-- Слоты просматриваемого --}}
                </span>
            </p>
        </div>

        {{-- Основной контент --}}
        <div class="container mx-auto px-0">
            {{-- Место для уведомлений (если нужно) --}}
            {{-- <div id="notification-container"></div> --}}

            @if($equippedItems->isEmpty())
                <div
                    class="bg-[#3b3a33]/70 border border-[#a6925e]/50 rounded-lg p-8 shadow-lg text-center mt-6 backdrop-blur-sm">
                    <p class="text-[#d9d3b8] italic">У игрока {{ $user->name }} нет экипированных предметов.</p>
                    {{-- Кнопка назад к профилю другого игрока --}}
                    <a href="{{ route('user.profile.other', ['id' => $user->id]) }}"
                        class="mt-4 inline-block bg-[#514b3c] text-[#d9d3b8] py-2 px-4 rounded shadow-md hover:bg-[#5d5745] transition duration-300 text-sm font-medium">
                        Назад к профилю
                    </a>
                </div>
            @else
                    <div class="grid grid-cols-1 gap-4 mb-6 mt-2">
                        @foreach($equippedItems as $equippedItem)
                                    @php
                                        // Данные предмета просматриваемого пользователя
                                        $item = $equippedItem->item;
                                        $qualityClassMap = [
                                            'Обычное' => 'common',
                                            'Необычное' => 'uncommon',
                                            'Редкое' => 'rare',
                                            'Эпическое' => 'epic',
                                            'Легендарное' => 'legendary',
                                        ];
                                        $qualitySlug = $qualityClassMap[$item->quality] ?? 'common';
                                        $qualityTextColor = $qualitySlug . '-text';
                                        $qualityBgColor = 'bg-' . $qualitySlug;

                                        // Русский комментарий: Определение цвета качества предмета для свечения
                                        $qualityColors = [
                                            'Обычное' => 'text-gray-200',
                                            'Необычное' => 'text-green-400',
                                            'Редкое' => 'text-blue-400',
                                            'Эпическое' => 'text-purple-400',
                                            'Легендарное' => 'text-orange-400',
                                        ];
                                        $qualityColor = $qualityColors[$item->quality] ?? 'text-gray-200';
                                        // Русский комментарий: Определение класса для цвета свечения
                                        $glowColorClass = str_replace('text-', 'glow-color-', $qualityColor);

                                        // --- Логика сравнения по СУММЕ АТРИБУТОВ ---
                                        $totalDifference = 0;            // Общая разница по всем атрибутам
                                        $statComparisonClass = '';       // CSS класс для цвета

                                        // Атрибуты, которые будем сравнивать
                                        $attributesToCompare = [
                                            'strength', 'intelligence', 'recovery', 'armor',
                                            'crit_chance', 'crit_damage', 'hp', 'mp',
                                            // Можно добавить и другие, если нужно, например:
                                            // 'resistance_fire', 'resistance_lightning'
                                        ];

                                        // Ищем предмет ТЕКУЩЕГО пользователя в том же слоте
                                        $currentUserItem = $currentUserEquippedItems->get($equippedItem->slot);

                                        // Проходим по каждому атрибуту для сравнения
                                        foreach ($attributesToCompare as $attribute) {
                                            // Получаем значение атрибута у предмета ДРУГОГО игрока (или 0)
                                            $otherItemValue = $equippedItem->{$attribute} ?? 0;

                                            // Получаем значение атрибута у предмета ТЕКУЩЕГО игрока (или 0, если предмета нет)
                                            $currentUserItemValue = 0;
                                            if ($currentUserItem) {
                                                $currentUserItemValue = $currentUserItem->{$attribute} ?? 0;
                                            }

                                            // Вычисляем разницу для этого атрибута и добавляем к общей сумме
                                            $totalDifference += ($otherItemValue - $currentUserItemValue);
                                        }

                                        // Определяем цвет на основе ОБЩЕЙ разницы
                                        if ($totalDifference > 0) {
                                            $statComparisonClass = 'stat-comparison-positive'; // Зеленый (в сумме лучше)
                                        } elseif ($totalDifference < 0) {
                                            $statComparisonClass = 'stat-comparison-negative'; // Коричневый (в сумме хуже)
                                        } else {
                                            $statComparisonClass = 'stat-comparison-neutral';  // Серый (в сумме равны)
                                        }
                                        // --- Конец логики сравнения по СУММЕ АТРИБУТОВ ---
                                    @endphp

                                    {{-- Карточка предмета --}}
                                    <div
                                        class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02] group">
                                        {{-- Фон и рамка --}}
                                        <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>
                                        <div
                                            class="absolute inset-0 rounded-lg border-2 border-[#a6925e]/70 pointer-events-none group-hover:border-[#e5b769]/90 transition-colors duration-300">
                                        </div>
                                        <div
                                            class="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50 group-hover:opacity-80 transition-opacity duration-300">
                                        </div>
                                        <div
                                            class="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50 group-hover:opacity-80 transition-opacity duration-300">
                                        </div>

                                        <div class="relative p-3">
                                            <div class="flex items-start space-x-3 mb-2.5">
                                                {{-- Русский комментарий: Иконка предмета с эффектом свечения по качеству --}}
                                                <div class="relative flex-shrink-0 animate-breathing-glow {{ $glowColorClass }}">
                                                    {{-- Убираем все рамки и фоновые контейнеры, оставляем только чистое изображение --}}
                                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                                        alt="{{ $item->name }}"
                                                        class="w-16 h-16 object-contain transform group-hover:scale-105 transition-transform duration-300"
                                                        style="image-rendering: crisp-edges;">
                                                    {{-- Отображение СУММАРНОЙ разницы атрибутов --}}
                                                    @if($totalDifference != 0)
                                                        <div
                                                            class="absolute -bottom-1 -right-1 px-1 py-0.5 bg-[#1f1c18]/90 rounded border border-[#a6925e]/70 shadow-sm flex items-center justify-center">
                                                            {{-- Выводим СУММАРНУЮ разницу с нужным классом цвета --}}
                                                            <span class="text-[9px] font-bold {{ $statComparisonClass }}">
                                                                {{-- Добавляем знак + только для положительных --}}
                                                                ({{ $totalDifference > 0 ? '+' : '' }}{{ round($totalDifference) }})
                                                            </span>
                                                        </div>
                                                    @endif
                                                </div>

                                                {{-- Информация о предмете --}}
                                                <div class="flex-1 min-w-0">
                                                    <a href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $equippedItem->id]) }}"
                                                        class="text-base font-bold {{ $qualityTextColor }} leading-tight mb-0.5 block truncate hover:text-yellow-300 transition-colors duration-200 text-shadow-sm"
                                                        title="{{ $item->name }}">
                                                        {{ $item->name }}
                                                    </a>

                                                    <div class="flex items-center text-xs mb-1 text-[#c7bfad] space-x-2 flex-wrap"> {{--
                                                        Добавлен flex-wrap на всякий случай --}}
                                                        <span
                                                            class="inline-flex items-center px-1.5 py-0.5 bg-[#38352c]/80 rounded border border-[#514b3c]/60 mb-0.5">
                                                            {{-- Добавлен mb-0.5 --}}
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5 text-[#a19a8a]"
                                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                                    d="M15 7a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2h6zM12 11h.01" />
                                                            </svg>
                                                            {{ $equippedItem->slot }}
                                                        </span>
                                                        <div class="inline-flex items-center mb-0.5"> {{-- Добавлен mb-0.5 --}}
                                                            <div class="w-2 h-2 rounded-full mr-1 {{ $qualityBgColor }}"></div>
                                                            <span class="{{ $qualityTextColor }}">{{ $item->quality }}</span>
                                                        </div>
                                                        {{-- Уровень заточки, если есть (добавлено из equipmentOther) --}}
                                                        @if ($equippedItem->current_upgrade_level > 0)
                                                            <span
                                                                class="inline-flex items-center px-1.5 py-0.5 bg-[#38352c]/80 rounded border border-[#514b3c]/60 mb-0.5 text-yellow-400">
                                                                +{{ $equippedItem->current_upgrade_level }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                    <div class="text-xs text-[#a19a8a]">
                                                        <i class="fas fa-shield-alt text-[10px] mr-0.5 opacity-70"></i>
                                                        Прочность: <span
                                                            class="font-medium text-[#c7bfad]">{{ $equippedItem->durability }}/{{ $equippedItem->max_durability }}</span>
                                                    </div>

                                                    @if($equippedItem->activeEffects && $equippedItem->activeEffects->count() > 0)
                                                        <div class="mt-1 text-xs">
                                                            <div class="flex items-center">
                                                                <span class="text-[#ffd700] text-[10px] mr-1">✨</span>
                                                                <span class="text-[#ffd700]">Активные эффекты:</span>
                                                            </div>
                                                            <div class="pl-2 mt-0.5 space-y-1">
                                                                @foreach($equippedItem->activeEffects as $effect)
                                                                    <div class="flex items-center justify-between">
                                                                        <span class="text-[#d9d3b8]">{{ $effect->effect_name }}</span>
                                                                        <span class="text-[#a6925e] text-[9px]">{{ $effect->getRemainingDuration() }} сек.</span>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            {{-- Кнопки (только "Подробнее") --}}
                                            <div class="grid grid-cols-1 gap-2 mt-2"> {{-- Используем grid-cols-1 т.к. кнопка одна --}}
                                                <a href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $equippedItem->id]) }}"
                                                    class="w-full bg-gradient-to-b from-[#514b3c]/90 to-[#38352c]/90 text-[#d9d3b8] py-1.5 px-2 text-sm font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c]/80 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                                        fill="currentColor">
                                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                                        <path fill-rule="evenodd"
                                                            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                    Подробнее
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                        @endforeach
                    </div>
            @endif
        </div>
    </div> {{-- Конец основного контейнера --}}

    {{-- Нижние кнопки навигации ТЕКУЩЕГО пользователя --}}
    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2 flex-shrink-0">
        <a href="{{ route('inventory.index') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300 text-xs">Рюкзак</a>
        <a href="{{ route('user.profile') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300 text-xs">Персонаж</a>
        <a href="#"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300 text-xs">Гильдия</a>
    </div>

   {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" /> 

    <script>
        // Обновление времени (оставляем)
        function updateServerTime() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const timeElement = document.getElementById('server-time');
            if (timeElement) { timeElement.textContent = `${hours}:${minutes}`; }
        }
        updateServerTime();
        setInterval(updateServerTime, 60000);
    </script>
</body>

</html>