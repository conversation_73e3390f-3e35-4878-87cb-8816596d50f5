{{--
    Компонент слота экипировки
    Отображает один слот снаряжения с предметом или пустой слот
--}}
@props([
    'slotName',
    'slotNumber',
    'equippedItem' => null,
    'item' => null,
    'isOwner' => true,
    'routePrefix' => 'user.equipped-item.details'
])

@php
    // Определяем цвета и классы для качества предмета
    $qualityClassMap = [
        'Обычное' => 'common',
        'Необычное' => 'uncommon',
        'Редкое' => 'rare',
        'Эпическое' => 'epic',
        'Легендарное' => 'legendary',
        'Мифическое' => 'mythic',
        'Божественное' => 'divine'
    ];

    $qualityClass = $item ? ($qualityClassMap[$item->quality] ?? 'common') : 'empty';

    // Русский комментарий: Определение цвета качества предмета для свечения
    $qualityColors = [
        'Обычное' => 'text-gray-200',
        'Необычное' => 'text-green-400',
        'Редкое' => 'text-blue-400',
        'Эпическое' => 'text-purple-400',
        'Легендарное' => 'text-orange-400',
        'Мифическое' => 'text-pink-400',
        'Божественное' => 'text-yellow-400'
    ];
    $qualityColor = $item ? ($qualityColors[$item->quality] ?? 'text-gray-200') : '';
    // Русский комментарий: Определение класса для цвета свечения
    $glowColorClass = $item ? str_replace('text-', 'glow-color-', $qualityColor) : '';
@endphp

{{-- Компактный дизайн слота в стиле оригинальной игры --}}
<div class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden {{ $glowClass }}">
    {{-- Заголовок слота --}}
    <div class="bg-gradient-to-b from-[#5a4d36] to-[#3a321c] border-b border-[#8c7a55] px-3 py-2">
        <h3 class="text-center text-[#e9d5a0] font-bold text-sm tracking-wide">
            {{ $slotName }}
        </h3>
    </div>

    @if($equippedItem && $item)
        {{-- Экипированный предмет в компактном стиле --}}
        <div class="p-3">
            <div class="flex items-center space-x-3">
                {{-- Русский комментарий: Иконка предмета с эффектом свечения по качеству --}}
                <div class="flex-shrink-0 animate-breathing-glow {{ $glowColorClass }}">
                    {{-- Убираем рамки и фон, оставляем только чистое изображение --}}
                    <img src="{{ $item->icon_url ?? '/assets/default-item.png' }}"
                         alt="{{ $item->name }}"
                         class="w-12 h-12 object-contain"
                         style="image-rendering: crisp-edges;">
                </div>

                {{-- Информация о предмете --}}
                <div class="flex-grow min-w-0">
                    @if($isOwner)
                        <a href="{{ route($routePrefix, $item->id) }}"
                           class="block hover:text-[#e5b769] transition-colors duration-200">
                    @endif
                            <h4 class="text-[#e9d5a0] font-medium text-sm truncate quality-{{ $qualityClass }}">
                                {{ $item->name }}
                            </h4>
                            <p class="text-[#a6925e] text-xs">
                                {{ $item->quality }}
                            </p>
                    @if($isOwner)
                        </a>
                    @endif
                </div>
            </div>
        </div>

        {{-- Компактные характеристики предмета --}}
        @if($item->damage || $item->armor || $item->magic_damage || $item->magic_armor)
            <div class="px-3 pb-2 border-t border-[#8c7a55]/50">
                <div class="flex flex-wrap gap-2 text-xs mt-2">
                    @if($item->damage)
                        <span class="text-red-400 bg-[#1a1a1a] px-2 py-1 rounded">
                            <i class="fas fa-sword mr-1"></i>{{ $item->damage }}
                        </span>
                    @endif
                    @if($item->armor)
                        <span class="text-blue-400 bg-[#1a1a1a] px-2 py-1 rounded">
                            <i class="fas fa-shield-alt mr-1"></i>{{ $item->armor }}
                        </span>
                    @endif
                    @if($item->magic_damage)
                        <span class="text-purple-400 bg-[#1a1a1a] px-2 py-1 rounded">
                            <i class="fas fa-magic mr-1"></i>{{ $item->magic_damage }}
                        </span>
                    @endif
                    @if($item->magic_armor)
                        <span class="text-cyan-400 bg-[#1a1a1a] px-2 py-1 rounded">
                            <i class="fas fa-shield mr-1"></i>{{ $item->magic_armor }}
                        </span>
                    @endif
                </div>
            </div>
        @endif
    @else
        {{-- Пустой слот в компактном стиле --}}
        <div class="p-3">
            <div class="flex items-center justify-center h-16 border border-dashed border-[#8c7a55]/50 rounded bg-[#1a1a1a]/50">
                <div class="text-center">
                    <i class="fas fa-plus text-[#8c7a55]/50 text-lg mb-1"></i>
                    <p class="text-[#8c7a55]/70 text-xs">Пусто</p>
                </div>
            </div>
        </div>
    @endif
</div>
